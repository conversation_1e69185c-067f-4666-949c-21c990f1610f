# Front-end Style Guide

## Layout

The designs were created to the following widths:

- Mobile: 375px
- Desktop: 1440px

> 💡 These are just the design sizes. Ensure content is responsive and meets WCAG requirements by testing the full range of screen sizes from 320px to large screens.

## Colors

### Primary

- Blue 600: hsl(231, 69%, 60%)
- Red 400: hsl(0, 94%, 66%)

### Neutral

- Grey 50: hsl(0, 0%, 97%)
- Blue 950: hsl(229, 31%, 21%)

## Typography

### Body Copy

- Font size: 18px

### Fonts

- Family: [Rubik](https://fonts.google.com/specimen/Rubik)
- Weights: 400, 500

## Icons

For the icons in the design, you can choose between using the icons provided or a font icon library.

Some library suggestions can be found below:

- [Font Awesome](https://fontawesome.com)
- [IcoMoon](https://icomoon.io)
- [Ionicons](https://ionicons.com)

> 💎 [Upgrade to Pro](https://www.frontendmentor.io/pro?ref=style-guide) for design file access to see all design details and get hands-on experience using a professional workflow with tools like Figma.
