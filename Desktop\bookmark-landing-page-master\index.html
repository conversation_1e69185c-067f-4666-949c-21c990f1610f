<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" type="image/png" sizes="32x32" href="./images/favicon-32x32.png">
  <title>Frontend Mentor | Bookmark landing page</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500&display=swap" rel="stylesheet">

  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    :root {
      --blue-600: hsl(231, 69%, 60%);
      --red-400: hsl(0, 94%, 66%);
      --grey-50: hsl(0, 0%, 97%);
      --blue-950: hsl(229, 31%, 21%);
      --white: #ffffff;
    }

    body {
      font-family: 'Rubik', sans-serif;
      font-size: 18px;
      line-height: 1.6;
      color: var(--blue-950);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Header */
    header {
      padding: 2rem 0;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;
      font-size: 1.2rem;
      text-decoration: none;
      color: var(--blue-950);
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 2rem;
      align-items: center;
    }

    .nav-links a {
      text-decoration: none;
      color: var(--blue-950);
      font-weight: 400;
      font-size: 0.9rem;
      letter-spacing: 1px;
      text-transform: uppercase;
      transition: color 0.3s ease;
    }

    .nav-links a:hover {
      color: var(--red-400);
    }

    .login-btn {
      background: var(--red-400);
      color: white !important;
      padding: 0.8rem 2rem;
      border-radius: 5px;
      border: 2px solid var(--red-400);
      transition: all 0.3s ease;
    }

    .login-btn:hover {
      background: white;
      color: var(--red-400) !important;
    }

    /* Hero Section */
    .hero {
      padding: 4rem 0;
    }

    .hero .container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 4rem;
      align-items: center;
      position: relative;
      overflow: hidden;
    }

    .hero-content h1 {
      font-size: 3rem;
      font-weight: 500;
      line-height: 1.1;
      margin-bottom: 1.5rem;
      color: var(--blue-950);
    }

    .hero-content p {
      color: #6b7280;
      margin-bottom: 2rem;
      font-size: 1.1rem;
    }

    .hero-buttons {
      display: flex;
      gap: 1rem;
    }

    .btn {
      padding: 1rem 1.5rem;
      border-radius: 5px;
      text-decoration: none;
      font-weight: 500;
      font-size: 0.9rem;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      cursor: pointer;
    }

    .btn-primary {
      background: var(--blue-600);
      color: white;
      border-color: var(--blue-600);
    }

    .btn-primary:hover {
      background: white;
      color: var(--blue-600);
      border-color: var(--blue-600);
    }

    .btn-secondary {
      background: var(--grey-50);
      color: var(--blue-950);
      border-color: var(--grey-50);
    }

    .btn-secondary:hover {
      background: white;
      border-color: var(--blue-950);
    }

    .hero-image {
      position: relative;
    }

    .hero-image::after {
      content: '';
      position: absolute;
      top: 20%;
      right: -50%;
      width: 100%;
      height: 80%;
      background: var(--blue-600);
      border-radius: 200px 0 0 200px;
      z-index: -1;
    }

    .hero-image img {
      width: 100%;
      height: auto;
    }

    /* Features Section */
    .features {
      padding: 6rem 0;
      text-align: center;
    }

    .features h2 {
      font-size: 2rem;
      font-weight: 500;
      margin-bottom: 1rem;
      color: var(--blue-950);
    }

    .features-intro {
      color: #6b7280;
      max-width: 500px;
      margin: 0 auto 3rem;
    }

    .features-tabs {
      display: flex;
      justify-content: center;
      border-bottom: 1px solid #e5e7eb;
      margin-bottom: 4rem;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .tab-btn {
      background: none;
      border: none;
      padding: 1.5rem 2rem;
      font-size: 1rem;
      color: #6b7280;
      cursor: pointer;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
      position: relative;
    }

    .tab-btn.active {
      color: var(--blue-950);
      border-bottom-color: var(--red-400);
    }

    .tab-btn:hover {
      color: var(--red-400);
    }

    .tab-content {
      display: none;
      text-align: left;
    }

    .tab-content.active {
      display: block;
    }

    .tab-content .container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 4rem;
      align-items: center;
    }

    .tab-image {
      position: relative;
    }

    .tab-image::after {
      content: '';
      position: absolute;
      top: 20%;
      left: -50%;
      width: 100%;
      height: 80%;
      background: var(--blue-600);
      border-radius: 0 200px 200px 0;
      z-index: -1;
    }

    .tab-image img {
      width: 100%;
      height: auto;
    }

    .tab-text h3 {
      font-size: 2rem;
      font-weight: 500;
      margin-bottom: 1.5rem;
      color: var(--blue-950);
    }

    .tab-text p {
      color: #6b7280;
      margin-bottom: 2rem;
    }

    /* Download Section */
    .download {
      padding: 6rem 0;
      text-align: center;
    }

    .download h2 {
      font-size: 2rem;
      font-weight: 500;
      margin-bottom: 1rem;
      color: var(--blue-950);
    }

    .download-intro {
      color: #6b7280;
      max-width: 500px;
      margin: 0 auto 4rem;
    }

    .browser-cards {
      display: flex;
      justify-content: center;
      gap: 2rem;
      max-width: 1000px;
      margin: 0 auto;
      align-items: flex-start;
    }

    .browser-card {
      background: white;
      border-radius: 15px;
      padding: 2.5rem 1.5rem;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      position: relative;
      width: 280px;
      flex-shrink: 0;
    }

    .browser-card:nth-child(1) {
      margin-top: 0;
    }

    .browser-card:nth-child(2) {
      margin-top: 2rem;
    }

    .browser-card:nth-child(3) {
      margin-top: 4rem;
    }

    .browser-card img {
      width: 100px;
      height: 100px;
      margin-bottom: 2rem;
    }

    .browser-card h3 {
      font-size: 1.2rem;
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: var(--blue-950);
    }

    .browser-card p {
      color: #6b7280;
      margin-bottom: 2rem;
      font-size: 0.9rem;
    }

    .browser-card::before {
      content: '';
      position: absolute;
      bottom: 80px;
      left: 0;
      right: 0;
      height: 4px;
      background: url('./images/bg-dots.svg') repeat-x;
      background-size: contain;
    }

    .browser-card .btn {
      width: 100%;
      justify-content: center;
      display: flex;
    }

    /* FAQ Section */
    .faq {
      padding: 6rem 0;
      text-align: center;
    }

    .faq h2 {
      font-size: 2rem;
      font-weight: 500;
      margin-bottom: 1rem;
      color: var(--blue-950);
    }

    .faq-intro {
      color: #6b7280;
      max-width: 500px;
      margin: 0 auto 4rem;
    }

    .faq-list {
      max-width: 600px;
      margin: 0 auto;
    }

    .faq-item {
      border-bottom: 1px solid #e5e7eb;
    }

    .faq-question {
      width: 100%;
      background: none;
      border: none;
      padding: 1.5rem 0;
      text-align: left;
      font-size: 1rem;
      color: var(--blue-950);
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: color 0.3s ease;
    }

    .faq-question:hover {
      color: var(--red-400);
    }

    .faq-question::after {
      content: '';
      width: 20px;
      height: 13px;
      background: url('./images/icon-arrow.svg') no-repeat center;
      transition: transform 0.3s ease;
    }

    .faq-question.active::after {
      transform: rotate(180deg);
    }

    .faq-answer {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
      color: #6b7280;
      text-align: left;
    }

    .faq-answer.active {
      max-height: 200px;
      padding-bottom: 1.5rem;
    }

    .faq .btn {
      margin-top: 3rem;
    }

    /* Newsletter Section */
    .newsletter {
      background: var(--blue-600);
      padding: 4rem 0;
      text-align: center;
      color: white;
    }

    .newsletter-count {
      font-size: 0.8rem;
      letter-spacing: 4px;
      text-transform: uppercase;
      margin-bottom: 1rem;
    }

    .newsletter h2 {
      font-size: 2rem;
      font-weight: 500;
      margin-bottom: 2rem;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }

    .newsletter-form {
      display: flex;
      gap: 1rem;
      max-width: 400px;
      margin: 0 auto;
    }

    .newsletter-form input {
      flex: 1;
      padding: 1rem;
      border: none;
      border-radius: 5px;
      font-size: 0.9rem;
    }

    .newsletter-form input::placeholder {
      color: #9ca3af;
    }

    .newsletter-form .btn {
      background: var(--red-400);
      border-color: var(--red-400);
      white-space: nowrap;
    }

    .newsletter-form .btn:hover {
      background: white;
      color: var(--red-400);
    }

    /* Footer */
    footer {
      background: var(--blue-950);
      padding: 2rem 0;
      color: white;
    }

    footer .container {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .footer-nav {
      display: flex;
      align-items: center;
      gap: 3rem;
    }



    .footer-links {
      display: flex;
      list-style: none;
      gap: 2rem;
    }

    .footer-links a {
      color: white;
      text-decoration: none;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: color 0.3s ease;
    }

    .footer-links a:hover {
      color: var(--red-400);
    }

    .social-links {
      display: flex;
      gap: 1.5rem;
    }

    .social-links a {
      color: white;
      transition: color 0.3s ease;
    }

    .social-links a:hover {
      color: var(--red-400);
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
      .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
      }

      .hero-content h1 {
        font-size: 2rem;
      }

      .tab-content .container {
        grid-template-columns: 1fr;
        text-align: center;
      }

      .browser-cards {
        flex-direction: column;
        align-items: center;
      }

      .browser-card:nth-child(1),
      .browser-card:nth-child(2),
      .browser-card:nth-child(3) {
        margin-top: 0;
        margin-bottom: 2rem;
      }

      .newsletter-form {
        flex-direction: column;
      }

      footer .container {
        flex-direction: column;
        gap: 2rem;
      }

      .footer-nav {
        flex-direction: column;
        gap: 2rem;
      }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header>
    <div class="container">
      <nav>
        <a href="#" class="logo">
          <img src="./images/logo-bookmark.svg" alt="Bookmark" width="148" height="25">
        </a>
        <ul class="nav-links">
          <li><a href="#features">Features</a></li>
          <li><a href="#pricing">Pricing</a></li>
          <li><a href="#contact">Contact</a></li>
          <li><a href="#" class="login-btn">Login</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="hero">
    <div class="container">
      <div class="hero-content">
        <h1>A Simple Bookmark Manager</h1>
        <p>A clean and simple interface to organize your favourite websites. Open a new browser tab and see your sites load instantly. Try it for free.</p>
        <div class="hero-buttons">
          <a href="#" class="btn btn-primary">Get it on Chrome</a>
          <a href="#" class="btn btn-secondary">Get it on Firefox</a>
        </div>
      </div>
      <div class="hero-image">
        <img src="./images/illustration-hero.svg" alt="Bookmark Manager Interface">
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="features" id="features">
    <div class="container">
      <h2>Features</h2>
      <p class="features-intro">Our aim is to make it quick and easy for you to access your favourite websites. Your bookmarks sync between your devices so you can access them on the go.</p>

      <div class="features-tabs">
        <button class="tab-btn active" onclick="showTab(0)">Simple Bookmarking</button>
        <button class="tab-btn" onclick="showTab(1)">Speedy Searching</button>
        <button class="tab-btn" onclick="showTab(2)">Easy Sharing</button>
      </div>

      <div class="tab-content active" id="tab-0">
        <div class="container">
          <div class="tab-image">
            <img src="./images/illustration-features-tab-1.svg" alt="Simple Bookmarking">
          </div>
          <div class="tab-text">
            <h3>Bookmark in one click</h3>
            <p>Organize your bookmarks however you like. Our simple drag-and-drop interface gives you complete control over how you manage your favourite sites.</p>
            <a href="#" class="btn btn-primary">More Info</a>
          </div>
        </div>
      </div>

      <div class="tab-content" id="tab-1">
        <div class="container">
          <div class="tab-image">
            <img src="./images/illustration-features-tab-2.svg" alt="Speedy Searching">
          </div>
          <div class="tab-text">
            <h3>Intelligent search</h3>
            <p>Our powerful search feature will help you find saved sites in no time at all. No need to trawl through all of your bookmarks.</p>
            <a href="#" class="btn btn-primary">More Info</a>
          </div>
        </div>
      </div>

      <div class="tab-content" id="tab-2">
        <div class="container">
          <div class="tab-image">
            <img src="./images/illustration-features-tab-3.svg" alt="Easy Sharing">
          </div>
          <div class="tab-text">
            <h3>Share your bookmarks</h3>
            <p>Easily share your bookmarks and collections with others. Create a shareable link that you can send at the click of a button.</p>
            <a href="#" class="btn btn-primary">More Info</a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Download Section -->
  <section class="download">
    <div class="container">
      <h2>Download the extension</h2>
      <p class="download-intro">We've got more browsers in the pipeline. Please do let us know if you've got a favourite you'd like us to prioritize.</p>

      <div class="browser-cards">
        <div class="browser-card">
          <img src="./images/logo-chrome.svg" alt="Chrome">
          <h3>Add to Chrome</h3>
          <p>Minimum version 62</p>
          <a href="#" class="btn btn-primary">Add & Install Extension</a>
        </div>

        <div class="browser-card">
          <img src="./images/logo-firefox.svg" alt="Firefox">
          <h3>Add to Firefox</h3>
          <p>Minimum version 55</p>
          <a href="#" class="btn btn-primary">Add & Install Extension</a>
        </div>

        <div class="browser-card">
          <img src="./images/logo-opera.svg" alt="Opera">
          <h3>Add to Opera</h3>
          <p>Minimum version 46</p>
          <a href="#" class="btn btn-primary">Add & Install Extension</a>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="faq">
    <div class="container">
      <h2>Frequently Asked Questions</h2>
      <p class="faq-intro">Here are some of our FAQs. If you have any other questions you'd like answered please feel free to email us.</p>

      <div class="faq-list">
        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(0)">
            What is Bookmark?
          </button>
          <div class="faq-answer" id="faq-0">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce tincidunt justo eget ultricies fringilla. Phasellus blandit ipsum quis quam ornare mattis.
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(1)">
            How can I request a new browser?
          </button>
          <div class="faq-answer" id="faq-1">
            Vivamus luctus eros aliquet convallis ultricies. Mauris augue massa, ultricies non ligula. Suspendisse imperdiet. Vivamus luctus eros aliquet convallis ultricies. Mauris augue massa, ultricies non ligula. Suspendisse imperdiet.
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(2)">
            Is there a mobile app?
          </button>
          <div class="faq-answer" id="faq-2">
            Sed consectetur quam id neque fermentum accumsan. Praesent luctus vestibulum dolor, ut condimentum urna vulputate eget. Cras in ligula quis est pharetra mattis sit amet pharetra purus. Sed sollicitudin ex et ultricies bibendum.
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(3)">
            What about other Chromium browsers?
          </button>
          <div class="faq-answer" id="faq-3">
            Integer condimentum ipsum id imperdiet finibus. Vivamus in placerat mi, at euismod dui. Aliquam vitae neque eget nisl gravida pellentesque non ut velit.
          </div>
        </div>
      </div>

      <a href="#" class="btn btn-primary">More Info</a>
    </div>
  </section>

  <!-- Newsletter Section -->
  <section class="newsletter">
    <div class="container">
      <p class="newsletter-count">35,000+ already joined</p>
      <h2>Stay up-to-date with what we're doing</h2>
      <form class="newsletter-form">
        <input type="email" placeholder="Enter your email address" required>
        <button type="submit" class="btn btn-primary">Contact Us</button>
      </form>
    </div>
  </section>

  <!-- Footer -->
  <footer>
    <div class="container">
      <div class="footer-nav">
        <a href="#" class="logo">
          <svg width="148" height="25" xmlns="http://www.w3.org/2000/svg">
            <g fill="none" fill-rule="evenodd">
              <path d="M37 6.299h5.227c.746 0 1.434.155 2.062.466.629.311 1.123.735 1.484 1.27s.542 1.12.542 1.754c0 .672-.165 1.254-.495 1.746-.33.491-.762.868-1.297 1.129v.15c.697.248 1.25.643 1.661 1.185.41.541.616 1.191.616 1.95 0 .735-.196 1.385-.588 1.951a3.817 3.817 0 0 1-1.587 1.307c-.665.305-1.403.457-2.212.457H37V6.299zm5.04 5.45c.548 0 .986-.152 1.316-.457.33-.305.495-.688.495-1.148 0-.448-.159-.824-.476-1.13-.318-.304-.738-.457-1.26-.457H39.52v3.192h2.52zm.28 5.619c.61 0 1.086-.159 1.428-.476.342-.317.513-.731.513-1.241 0-.51-.174-.927-.522-1.251-.349-.324-.847-.485-1.494-.485H39.52v3.453h2.8zm12.927 2.595c-1.307 0-2.492-.308-3.556-.924a6.711 6.711 0 0 1-2.511-2.53c-.61-1.07-.915-2.246-.915-3.528 0-1.281.305-2.457.915-3.528a6.711 6.711 0 0 1 2.51-2.529C52.756 6.308 53.94 6 55.248 6c1.306 0 2.492.308 3.556.924a6.711 6.711 0 0 1 2.51 2.53c.61 1.07.915 2.246.915 3.527 0 1.282-.305 2.458-.915 3.528a6.711 6.711 0 0 1-2.51 2.53c-1.064.616-2.25.924-3.556.924zm0-2.39a4.52 4.52 0 0 0 2.258-.578 4.177 4.177 0 0 0 1.615-1.624c.392-.697.588-1.494.588-2.39 0-.896-.196-1.692-.588-2.389a4.177 4.177 0 0 0-1.615-1.624 4.52 4.52 0 0 0-2.258-.579 4.47 4.47 0 0 0-2.25.579 4.195 4.195 0 0 0-1.605 1.624c-.392.697-.588 1.493-.588 2.39 0 .895.196 1.692.588 2.389a4.195 4.195 0 0 0 1.605 1.624 4.47 4.47 0 0 0 2.25.578zm15.353 2.39c-1.307 0-2.492-.308-3.556-.924a6.711 6.711 0 0 1-2.51-2.53c-.61-1.07-.915-2.246-.915-3.528 0-1.281.305-2.457.914-3.528a6.711 6.711 0 0 1 2.511-2.529C68.108 6.308 69.294 6 70.6 6c1.307 0 2.492.308 3.556.924a6.711 6.711 0 0 1 2.51 2.53c.61 1.07.915 2.246.915 3.527 0 1.282-.305 2.458-.914 3.528a6.711 6.711 0 0 1-2.511 2.53c-1.064.616-2.25.924-3.556.924zm0-2.39a4.52 4.52 0 0 0 2.259-.578 4.177 4.177 0 0 0 1.614-1.624c.392-.697.588-1.494.588-2.39 0-.896-.196-1.692-.588-2.389a4.177 4.177 0 0 0-1.614-1.624 4.52 4.52 0 0 0-2.259-.579 4.47 4.47 0 0 0-2.25.579 4.195 4.195 0 0 0-1.605 1.624c-.392.697-.588 1.493-.588 2.39 0 .895.196 1.692.588 2.389a4.195 4.195 0 0 0 1.606 1.624 4.47 4.47 0 0 0 2.249.578zM79.83 6.3h2.52v5.73h.15l4.89-5.73h3.043v.149L85.6 11.973l5.338 7.542v.149h-3.08l-3.994-5.693-1.512 1.773v3.92h-2.52V6.299zM93.779 6h3.248l3.546 9.39h.15L104.268 6h3.267v13.365h-2.501v-6.589l.15-2.221h-.15l-3.398 8.81h-1.96l-3.416-8.81h-.149l.15 2.221v6.59h-2.483V6zm20.8 0h2.894l5.021 13.365h-2.781l-1.12-3.192h-5.115l-1.12 3.192h-2.781L114.579 6zm3.193 7.859l-1.176-3.36-.486-1.606h-.149l-.485 1.606-1.195 3.36h3.49zM124.553 6h4.872c.871 0 1.646.18 2.324.541.678.361 1.204.862 1.577 1.503.374.64.56 1.366.56 2.175 0 .858-.27 1.62-.812 2.286a4.617 4.617 0 0 1-2.044 1.447l-.018.13 3.584 5.134v.15h-2.894l-3.453-5.022h-1.176v5.021h-2.52V6zm4.853 6.03c.573 0 1.04-.175 1.4-.523.361-.349.542-.79.542-1.326 0-.51-.172-.945-.514-1.306-.342-.361-.806-.542-1.39-.542h-2.371v3.696h2.333zm7.23-6.03h2.52v5.73h.15l4.89-5.73h3.043v.15l-4.835 5.525 5.34 7.541v.15h-3.08l-3.996-5.694-1.512 1.773v3.92h-2.52V6z" fill="white" fill-rule="nonzero"/>
              <g>
                <circle fill="white" cx="12.5" cy="12.5" r="12.5"/>
                <path d="M9 9v10l3.54-3.44L16.078 19V9a2 2 0 0 0-2-2H11a2 2 0 0 0-2 2z" fill="#5267DF"/>
              </g>
            </g>
          </svg>
        </a>
        <ul class="footer-links">
          <li><a href="#features">Features</a></li>
          <li><a href="#pricing">Pricing</a></li>
          <li><a href="#contact">Contact</a></li>
        </ul>
      </div>
      <div class="social-links">
        <a href="#" aria-label="Facebook">
          <img src="./images/icon-facebook.svg" alt="Facebook" width="24" height="24">
        </a>
        <a href="#" aria-label="Twitter">
          <img src="./images/icon-twitter.svg" alt="Twitter" width="24" height="20">
        </a>
      </div>
    </div>
  </footer>

  <script>
    function showTab(index) {
      // Remove active class from all tabs and buttons
      document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

      // Add active class to selected tab and button
      document.querySelectorAll('.tab-btn')[index].classList.add('active');
      document.getElementById(`tab-${index}`).classList.add('active');
    }

    function toggleFaq(index) {
      const question = document.querySelectorAll('.faq-question')[index];
      const answer = document.getElementById(`faq-${index}`);

      question.classList.toggle('active');
      answer.classList.toggle('active');
    }
  </script>
</body>
</html>